Resources:
  AppHeroRootAPIGWInvokePermission:
    Type: 'AWS::Lambda::Permission'
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:apphero-backend-service-${self:provider.stage}
      Action: 'lambda:InvokeFunction'
      Principal: 'apigateway.amazonaws.com'
      SourceArn: 'arn:aws:execute-api:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.APPHERO_API_ID}/*/*/apphero/*'
  UnauthAppHeroRootAPIGWInvokePermission:
    Type: 'AWS::Lambda::Permission'
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:apphero-backend-service-${self:provider.stage}
      Action: 'lambda:InvokeFunction'
      Principal: 'apigateway.amazonaws.com'
      SourceArn: 'arn:aws:execute-api:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.APPHERO_API_ID}/*/*/unauth/apphero/*'
  AppHeroCountryAPIGWInvokePermission:
    Type: 'AWS::Lambda::Permission'
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:apphero-backend-service-${self:provider.stage}
      Action: 'lambda:InvokeFunction'
      Principal: 'apigateway.amazonaws.com'
      SourceArn: 'arn:aws:execute-api:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.APPHERO_API_ID}/*/GET/apphero/lookup/country'
  AppHeroUpdateConsentDetailsAPIGWInvokePermission:
    Type: 'AWS::Lambda::Permission'
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:apphero-backend-service-${self:provider.stage}
      Action: 'lambda:InvokeFunction'
      Principal: 'apigateway.amazonaws.com'
      SourceArn: 'arn:aws:execute-api:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.APPHERO_API_ID}/*/PATCH/apphero/updateAppheroConsent'
  appSyncLogAccessRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: 'Allow'
            Principal:
              Service:
                - 'appsync.amazonaws.com'
            Action:
              - 'sts:AssumeRole'
      Description: 'IAM role for AppSync'
      RoleName: appsync-logs-${sls:stage}
      Policies:
        - PolicyName: 'appsync-logs-${sls:stage}'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Action:
                  - 'logs:CreateLogStream'
                  - 'logs:CreateLogGroup'
                  - 'logs:PutLogEvents'
                Resource:
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:*'
  appSyncDynamoDBServiceRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: 'Allow'
            Principal:
              Service:
                - 'appsync.amazonaws.com'
            Action:
              - 'sts:AssumeRole'
      Description: 'IAM role for AppSync to access dynamodb tables'
      RoleName: appSyncDynamoDBServiceRole-${sls:stage}
      Policies:
        - PolicyName: 'appSyncDynamoDBServicePolicy-${sls:stage}'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Action:
                  - 'dynamodb:*'
                Resource:
                  - !Sub 'arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*'
  # IAM role for Lambda execution with Parameter Store access
  appheroLambdaExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: 'Allow'
            Principal:
              Service:
                - 'lambda.amazonaws.com'
            Action:
              - 'sts:AssumeRole'
      Description: 'IAM role for AppHero Lambda with Parameter Store access'
      RoleName: apphero-lambda-exec-role-${sls:stage}
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
        - arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess
      Policies:
        - PolicyName: 'apphero-parameter-store-policy-${sls:stage}'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Action:
                  - 'ssm:GetParameter'
                  - 'ssm:GetParameters'
                  - 'ssm:GetParametersByPath'
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/apphero/${sls:stage}/oauth/*'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/apphero/${sls:stage}/*'
              - Effect: 'Allow'
                Action:
                  - 'kms:Decrypt'
                Resource:
                  - !Sub 'arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/*'
                Condition:
                  StringEquals:
                    'kms:ViaService': !Sub 'ssm.${AWS::Region}.amazonaws.com'
        - PolicyName: 'apphero-dynamodb-policy-${sls:stage}'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Action:
                  - 'dynamodb:*'
                Resource:
                  - !Sub 'arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/apphero-*'
        - PolicyName: 'apphero-secrets-manager-policy-${sls:stage}'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Action:
                  - 'secretsmanager:GetSecretValue'
                  - 'secretsmanager:UpdateSecret'
                Resource:
                  - !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:salesforce-gus-*'
        - PolicyName: 'apphero-s3-policy-${sls:stage}'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Action:
                  - 's3:GetObject'
                  - 's3:PutObject'
                  - 's3:DeleteObject'
                Resource:
                  - !Sub 'arn:aws:s3:::apphero-*/*'
                  - !Sub 'arn:aws:s3:::reviewcenter*/*'
              - Effect: 'Allow'
                Action:
                  - 's3:ListBucket'
                Resource:
                  - !Sub 'arn:aws:s3:::apphero-*'
                  - !Sub 'arn:aws:s3:::reviewcenter*'
        - PolicyName: 'apphero-sts-policy-${sls:stage}'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Action:
                  - 'sts:AssumeRole'
                Resource:
                  - 'arn:aws:iam::************:role/s3CrossAccountAccessRole-prod'
